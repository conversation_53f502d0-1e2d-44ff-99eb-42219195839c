import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/colors.dart';
import 'bug_report_page.dart';

class HelpSupportPage extends StatefulWidget {
  const HelpSupportPage({super.key});

  @override
  State<HelpSupportPage> createState() => _HelpSupportPageState();
}

class _HelpSupportPageState extends State<HelpSupportPage> {
  final TextEditingController _messageController = TextEditingController();
  String _selectedCategory = 'general';
  bool _isSubmitting = false;

  final List<Map<String, dynamic>> _faqItems = [
    {
      'question': 'How do I create a great profile?',
      'answer':
          'Use high-quality photos that show your face clearly, write an engaging bio that reflects your personality, and be honest about your interests and what you\'re looking for.',
      'category': 'profile',
    },
    {
      'question': 'How does matching work?',
      'answer':
          'When you like someone and they like you back, it\'s a match! You can then start messaging each other. Super likes increase your chances of matching.',
      'category': 'matching',
    },
    {
      'question': 'What are coins and how do I use them?',
      'answer':
          'Coins are Friendy\'s virtual currency. Use them for Super Likes, Boosts, Rewinds, and other premium features. You can purchase coins in the app.',
      'category': 'coins',
    },
    {
      'question': 'How do I report someone?',
      'answer':
          'Go to their profile, tap the three dots menu, and select "Report". Choose the appropriate reason and provide details if needed.',
      'category': 'safety',
    },
    {
      'question': 'Can I change my location?',
      'answer':
          'Yes, you can change your location in Settings > Discovery. This is useful when traveling or if you want to meet people in a different area.',
      'category': 'settings',
    },
    {
      'question': 'How do I delete my account?',
      'answer':
          'Go to Settings > Privacy > Delete Account. Note that this action is permanent and cannot be undone.',
      'category': 'account',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundDark,
      appBar: AppBar(
        title: const Text(
          'Help & Support',
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.backgroundDark,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Quick Actions
            _buildQuickActions(),

            const SizedBox(height: 24),

            // FAQ Section
            _buildFAQSection(),

            const SizedBox(height: 24),

            // Contact Support
            _buildContactSupport(),

            const SizedBox(height: 24),

            // Additional Resources
            _buildAdditionalResources(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardDark,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Quick Actions',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          _buildActionTile(
            icon: Icons.chat_bubble_outline,
            title: 'Live Chat',
            subtitle: 'Chat with our support team',
            onTap: _startLiveChat,
          ),
          _buildActionTile(
            icon: Icons.email_outlined,
            title: 'Email Support',
            subtitle: 'Send us an email',
            onTap: _sendEmail,
          ),
          _buildActionTile(
            icon: Icons.phone_outlined,
            title: 'Call Support',
            subtitle: 'Speak with a representative',
            onTap: _callSupport,
          ),
          _buildActionTile(
            icon: Icons.bug_report_outlined,
            title: 'Report a Bug',
            subtitle: 'Help us improve the app',
            onTap: _reportBug,
          ),
        ],
      ),
    );
  }

  Widget _buildFAQSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardDark,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Frequently Asked Questions',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ..._faqItems.map((faq) => _buildFAQItem(faq)),
        ],
      ),
    );
  }

  Widget _buildFAQItem(Map<String, dynamic> faq) {
    return ExpansionTile(
      title: Text(
        faq['question'],
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      iconColor: AppColors.primaryBlue,
      collapsedIconColor: Colors.white70,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Text(
            faq['answer'],
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactSupport() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardDark,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Contact Support',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Category Selection
            const Text(
              'Category',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: AppColors.backgroundDark,
                borderRadius: BorderRadius.circular(12),
              ),
              child: DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                ),
                dropdownColor: AppColors.cardDark,
                style: const TextStyle(color: Colors.white),
                items: const [
                  DropdownMenuItem(
                      value: 'general', child: Text('General Question')),
                  DropdownMenuItem(
                      value: 'technical', child: Text('Technical Issue')),
                  DropdownMenuItem(
                      value: 'account', child: Text('Account Problem')),
                  DropdownMenuItem(
                      value: 'billing', child: Text('Billing & Payments')),
                  DropdownMenuItem(
                      value: 'safety', child: Text('Safety & Security')),
                  DropdownMenuItem(value: 'feedback', child: Text('Feedback')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedCategory = value!;
                  });
                },
              ),
            ),

            const SizedBox(height: 16),

            // Message Input
            const Text(
              'Message',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: AppColors.backgroundDark,
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextField(
                controller: _messageController,
                maxLines: 5,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: 'Describe your issue or question...',
                  hintStyle: TextStyle(color: Colors.white54),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isSubmitting ? null : _submitSupportRequest,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryBlue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'Send Message',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalResources() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.cardDark,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              'Additional Resources',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          _buildActionTile(
            icon: Icons.article_outlined,
            title: 'User Guide',
            subtitle: 'Complete guide to using Friendy',
            onTap: _openUserGuide,
          ),
          _buildActionTile(
            icon: Icons.security_outlined,
            title: 'Safety Tips',
            subtitle: 'Stay safe while dating online',
            onTap: _openSafetyTips,
          ),
          _buildActionTile(
            icon: Icons.forum_outlined,
            title: 'Community Forum',
            subtitle: 'Connect with other users',
            onTap: _openCommunityForum,
          ),
          _buildActionTile(
            icon: Icons.star_outline,
            title: 'Rate the App',
            subtitle: 'Leave a review on the app store',
            onTap: _rateApp,
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.primaryBlue,
        size: 24,
      ),
      title: Text(
        title,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: Colors.white70,
          fontSize: 14,
        ),
      ),
      trailing: const Icon(
        Icons.chevron_right,
        color: Colors.white70,
      ),
      onTap: onTap,
    );
  }

  void _startLiveChat() {
    // Implement live chat functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Live chat will be available soon!'),
        backgroundColor: AppColors.primaryBlue,
      ),
    );
  }

  void _sendEmail() async {
    final uri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query:
          'subject=Friendy App Support Request&body=Please describe your issue or question here...',
    );
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _callSupport() async {
    final uri = Uri(scheme: 'tel', path: '******-FRIENDY');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _reportBug() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const BugReportPage(),
      ),
    );
  }

  Future<void> _submitSupportRequest() async {
    if (_messageController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a message'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isSubmitting = false;
    });

    _messageController.clear();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text(
              'Support request submitted successfully! We\'ll get back to you within 24 hours.'),
          backgroundColor: AppColors.primaryBlue,
        ),
      );
    }
  }

  void _openUserGuide() async {
    final uri = Uri.parse('https://friendy.app/guide');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _openSafetyTips() async {
    final uri = Uri.parse('https://friendy.app/safety');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _openCommunityForum() async {
    final uri = Uri.parse('https://community.friendy.app');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _rateApp() async {
    // Open app store for rating
    final uri = Uri.parse(
        'https://play.google.com/store/apps/details?id=com.friendy.app');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }
}
